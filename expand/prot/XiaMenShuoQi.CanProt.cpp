#include "expand.message.h"
#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <thread>
#include <unistd.h>
#include <sys/time.h>
#include <fstream>
#include <set>
#include <queue>
#include <cstdlib>
#include <chrono>
#include <errno.h>
#include <string.h>

#include "expand.h"
#include "devicehub.h"
#include "XiaMenShuoQi.CanProt.h"
#include <sys/system_properties.h>
#include "mystd.h"

namespace minieye {

#define XIAMEN_SHUOQI_CAN_PROT_CMD_HELP       0
#define XIAMEN_SHUOQI_CAN_PROT_CMD_SHOW       1
#define XIAMEN_SHUOQI_CAN_PROT_CMD_TRIGGER    2
#define XIAMEN_SHUOQI_CAN_PROT_CMD_GENATT     3
#define XIAMEN_SHUOQI_CAN_PROT_CMD_LOGLEVEL   4

static const std::vector<CmdStrT> gXiaMenShuoQiCanProtCmds = {
    {
        "help",
        XIAMEN_SHUOQI_CAN_PROT_CMD_HELP,
        "show this usage.\n"
    },
    {
        "show",
        XIAMEN_SHUOQI_CAN_PROT_CMD_SHOW,
        "show last alarm data.\n"
    },
    {
        "trigger",
        XIAMEN_SHUOQI_CAN_PROT_CMD_TRIGGER,
        "fake to trigger alarm event. Usage: trigger <event_type> <level>\n"
        "  event_types: aeb_warning, aeb_braking, ldw_left, ldw_right\n"
        "  levels: 1, 2\n"
    },
    {
        "genAtt",
        XIAMEN_SHUOQI_CAN_PROT_CMD_GENATT,
        "generate attachment file. Usage: genAtt <path>\n"
    },
    {
        "loglevel",
        XIAMEN_SHUOQI_CAN_PROT_CMD_LOGLEVEL,
        "set log level for parse messages. Usage: loglevel <level>\n"
        "  level: 1 (no parse logs), 2 (show parse logs)\n"
    },
};

XiaMenShuoQiCanProt::XiaMenShuoQiCanProt() {
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "XiaMenShuoQiCanProt%p", this);
    this->start();
}

XiaMenShuoQiCanProt::~XiaMenShuoQiCanProt() {
}

bool XiaMenShuoQiCanProt::onAlgoEvent(std::shared_ptr<Event> e) {
    switch (e->type()) {
        case EVT_TYPE_BSD_Left:
        case EVT_TYPE_BSD_Right: {
            if (e->c.level != 3) {
                std::lock_guard<std::mutex> lg(mAlgoRcdMapMtx);
                mLastAlgoEvtMap.insert({{e->type(), e->c.level}, my::timestamp::now()});
            }
            break;
        }
        case EVT_TYPE_DMS_ABSENCE:
        case EVT_TYPE_DMS_FATIGUE_Eye:
        case EVT_TYPE_HOD_HANDSOFF: {
            if (e->c.level == 1) {
                std::lock_guard<std::mutex> lg(mAlgoRcdMapMtx);
                mLastAlgoEvtMap.insert({{e->type(), e->c.level}, my::timestamp::now()});
            }
            break;
        }

        default: {
            break;
        }
    }
    return true;
}

int32_t XiaMenShuoQiCanProt::onServerConnected(void) {
    logd("XiaMenShuoQiCanProt server connected");
    // std::vector<uint32_t> can1List = {
    //     0x0CF00300,
    //     0x0CF02FA0,
    //     0x0CFDCC27,
    //     0x0C0BA027,
    //     0x10F007E8,
    //     0x18F0010B,
    //     0x18F00503,
    //     0x18FE5BE8,
    //     0x0CF0EEA0,
    //     0x18FEBF0B,
    //     0x18F0090B,
    //     0x0CF901B2,
    // };

    for (int i = 0; i < 5; i++) {
        std::map<uint32_t, uint32_t> vcanId = {
            {0x0C000000, 0xFF000000}, {0x10000000, 0xFF000000}, {0x18000000, 0xFF000000}};
        if (!DeviceHub::getInstance().setCanFilter(
                DeviceHub::DEVICE_TYPE_CAN0, MCU_CAN_IDX_CAN0_SPEED, false, vcanId)) {
            loge("set can filter failed");
        } else {
            logd("set can filter success");
        }
    }
    return 0;
}

int32_t XiaMenShuoQiCanProt::onServerDisconnected(void) {
    logd("XiaMenShuoQiCanProt server disconnected");
    return 0;
}

std::string XiaMenShuoQiCanProt::setupCmdList(const char *cmdName) {
    mCmdUsage = CmdStrT::setupCmdList(gXiaMenShuoQiCanProtCmds, cmdName);
    return mCmdUsage;
}

bool XiaMenShuoQiCanProt::runCmd(int argc, char **argv, std::string &ack) {
    uint32_t cmd = CmdStrT::strToCmd(gXiaMenShuoQiCanProtCmds, argv[0]);

    switch (cmd) {
        case XIAMEN_SHUOQI_CAN_PROT_CMD_HELP: {
            ack = mCmdUsage;
            return true;
        }

        case XIAMEN_SHUOQI_CAN_PROT_CMD_SHOW: {
            std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
            char info[2048];
            snprintf(info, sizeof(info),
                "\n=== XiaMenShuoQi CAN Protocol Status ===\n"
                "Last Alarm ID: %u\n"
                "Alarm Event Type: %u\n"
                "Alarm Level: %u\n"
                "Target Obstacle Type: %u\n"
                "TTC Collision Time: %u (x100ms)\n"
                "Vehicle Speed: %u kmph, %f kmph\n"
                "Longitudinal Distance: %u (x0.1m)\n"
                "Lateral Distance: %u (x0.1m)\n"
                "Forward Collision Warning Level: %u\n"
                "Lane Departure Warning: %u\n"
                "Gear Status: %u\n"
                "Brake Pedal Opening: %u%%\n"
                "Accelerator Pedal Opening: %u%%\n"
                "Steering Wheel Angle: %u\n"
                "Latitude: %u (x10^-6 deg)\n"
                "Longitude: %u (x10^-6 deg)\n"
                "Altitude: %u m\n"
                "Real-time Data Queue Size: %zu\n",
                mAlarmEventData.alarm_id,
                mAlarmEventData.alarm_event_type,
                mAlarmEventData.alarm_level,
                mAlarmEventData.target_obstacle_type,
                mAlarmEventData.ttc_collision_time,
                mAlarmEventData.vehicle_speed, mVehicleSpeed,
                mAlarmEventData.longitudinal_relative_distance,
                mAlarmEventData.lateral_relative_distance,
                mAlarmEventData.forward_collision_warning_level,
                mAlarmEventData.lane_departure_warning,
                mAlarmEventData.gear_status,
                mAlarmEventData.brake_pedal_opening,
                mAlarmEventData.accelerator_pedal_opening,
                mAlarmEventData.steering_wheel_angle,
                mAlarmEventData.latitude,
                mAlarmEventData.longitude,
                mAlarmEventData.altitude,
                mAttDataList.size());
            for (auto [it1, it2] : mCanIdTs) {
                snprintf(info + strlen(info), sizeof(info) - strlen(info),
                    "CAN ID 0x%08x, last received %5.0fms ago\n", it1, it2.elapsed());
            }
            ack = info;
            return true;
        }

        case XIAMEN_SHUOQI_CAN_PROT_CMD_TRIGGER: {
            if (argc >= 3) {
                return triggerFakeEvent(argv[1], argv[2], ack);
            } else {
                ack = "Usage: trigger <event_type> <level>\n"
                      "event_types: aeb_warning, aeb_braking, ldw_left, ldw_right\n"
                      "levels: 1, 2\n";
                return false;
            }
        }

        case XIAMEN_SHUOQI_CAN_PROT_CMD_GENATT: {
            if (argc >= 2) {
                return prepareAttachmentFile(argv[1], ack);
            } else {
                ack = "Usage: genAtt <path>\n"
                      "  path: path where attachment file will be created\n";
                return false;
            }
        }

        case XIAMEN_SHUOQI_CAN_PROT_CMD_LOGLEVEL: {
            if (argc >= 2) {
                uint32_t level = atoi(argv[1]);
                if (level == 1 || level == 2) {
                    mLogLevel = level;
                    char info[128];
                    snprintf(info, sizeof(info), "Log level set to %u\n", level);
                    ack = info;
                    return true;
                } else {
                    ack = "Invalid log level. Use 1 (no parse logs) or 2 (show parse logs)\n";
                    return false;
                }
            } else {
                ack = "Usage: loglevel <level>\n"
                      "  level: 1 (no parse logs), 2 (show parse logs)\n";
                return false;
            }
        }

        default: {
            ack = mCmdUsage;
            return false;
        }
    }
}

int32_t XiaMenShuoQiCanProt::onDataRecevied(const char *p, uint32_t len) {
    // logd("received data %d bytes", len);

    if (len >= sizeof(canFrame_t)) {
        canFrame_t *canFrame = (canFrame_t *)p;
        onCanMessageReceived(canFrame);
    }

    return 0;
}

// CAN消息接收处理
bool XiaMenShuoQiCanProt::onCanMessageReceived(canFrame_t *canFrame) {
    if (!canFrame) {
        return false;
    }
    mCanIdTs[canFrame->frameId] = my::timestamp::now();
    switch (canFrame->frameId) {
        case 0x0CF02FA0:  // AEBS1
            parseAEBS1Message(canFrame);
            break;
        case 0x0C0BA027:  // AEBS2_27
            parseAEBS2_27Message(canFrame);
            break;
        case 0x10F007E8:  // FLI1
            parseFLI1Message(canFrame);
            break;
        case 0x18FE5BE8:  // FLI2
            parseFLI2Message(canFrame);
            break;
        case 0x0CF0EEA0:  // WEDR
            parseWEDRMessage(canFrame);
            break;
        case 0x18F0010B:  // EBC1
            parseEBC1Message(canFrame);
            break;
        case 0x18FEBF0B:  // EBC2
            parseEBC2Message(canFrame);
            break;
        case 0x18F0090B:  // VDC2
            parseVDC2Message(canFrame);
            break;
        case 0xCF00300:  // EEC2
            parseEEC2Message(canFrame);
            break;
        case 0x18F00503:  // ETC2
            parseETC2Message(canFrame);
            break;
        case 0x0CFDCC27:  // OEL_27
            parseOEL_27Message(canFrame);
            break;
        case 0x0CF901B2:  // UDAS
            parseUDASMessage(canFrame);
            break;
        default:
            logd("Unknown CAN ID: 0x%X", canFrame->frameId);
            break;
    }

    return true;
}

bool XiaMenShuoQiCanProt::parseAEBS1Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mAEBS1Pkt, canFrame, sizeof(canFrame_t));

    if (mLogLevel >= 2) {
        logd("AEBS1: state=%d, warning_level=%d, object_detected=%d, ttc=%d",
             mAEBS1Pkt.advanced_emergency_braking_system_state,
             mAEBS1Pkt.collision_warning_level,
             mAEBS1Pkt.relevant_object_detected,
             mAEBS1Pkt.time_to_collision);
    }

    switch ((CollisionWarningLevel)mAEBS1Pkt.collision_warning_level) {
        case CollisionWarningLevel::WARNING_LEVEL_1:
        case CollisionWarningLevel::WARNING_LEVEL_3: {
            mAlarmEventData.target_obstacle_type = (uint8_t)TargetObstacleType::VEHICLE;
            break;
        }
        case CollisionWarningLevel::WARNING_LEVEL_2:
        case CollisionWarningLevel::WARNING_LEVEL_4: {
            mAlarmEventData.target_obstacle_type = (uint8_t)TargetObstacleType::PEDESTRIAN;
            break;
        }
        default: {
            mAlarmEventData.target_obstacle_type = (uint8_t)TargetObstacleType::INVALID;
            break;
        }
    }

    if (mAEBS1Pkt.time_to_collision <= 250) {
        mAlarmEventData.ttc_collision_time = mAEBS1Pkt.time_to_collision / 2;
    }

    switch ((AEBSSystemState)mAEBS1Pkt.advanced_emergency_braking_system_state) {
        case AEBSSystemState::EMERGENCY_BRAKING_ACTIVE: {
            mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::AEB_BRAKING;
            mAlarmEventData.alarm_level = (uint8_t)AlarmLevel::LEVEL_1;
            break;
        }
        case AEBSSystemState::COLLISION_WARNING_ACTIVE: {
            mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::AEB_WARNING;
            mAlarmEventData.alarm_level = (uint8_t)AlarmLevel::LEVEL_1;
            break;
        }
        case AEBSSystemState::COLLISION_WARNING_WITH_BRAKING: {
            mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::AEB_WARNING;
            mAlarmEventData.alarm_level = (uint8_t)AlarmLevel::LEVEL_2;
            break;
        }
        default: {
            return true;
        }
    }

    report();

    return true;
}

bool XiaMenShuoQiCanProt::parseAEBS2_27Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mAEBS2_27Pkt, canFrame, sizeof(canFrame_t));

    if (mLogLevel >= 2) {
        logd("AEBS2_27: driver_activation_demand=%d", mAEBS2_27Pkt.driver_activation_demand);
    }

    if (mAEBS2_27Pkt.driver_activation_demand == (uint8_t)AEBSDriverActivationDemand::DRIVER_DEACTIVATION) {
        mAlarmEventData.aeb_brake_switch_status = 0;
    } else if (mAEBS2_27Pkt.driver_activation_demand == (uint8_t)AEBSDriverActivationDemand::DRIVER_ACTIVATION) {
        mAlarmEventData.aeb_brake_switch_status = 1;
    } else {
        mAlarmEventData.aeb_brake_switch_status = 0xFF;
    }

    return true;
}

bool XiaMenShuoQiCanProt::parseFLI1Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mFLI1Pkt, canFrame, sizeof(canFrame_t));

    if (mLogLevel >= 2) {
        logd("FLI1: right_ldw=%d, left_ldw=%d",
             mFLI1Pkt.lane_departure_imminent_right_side,
             mFLI1Pkt.lane_departure_imminent_left_side);
    }

    if (mFLI1Pkt.lane_departure_imminent_left_side == (uint8_t)LaneDepartureStatus::IMMINENT) {
        mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::LANE_DEPARTURE_WARNING;
        mAlarmEventData.lane_departure_warning = (uint8_t)LaneDepartureWarning::LEFT_BOUNDARY_WARNING;
    } else if (mFLI1Pkt.lane_departure_imminent_right_side == (uint8_t)LaneDepartureStatus::IMMINENT) {
        mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::LANE_DEPARTURE_WARNING;
        mAlarmEventData.lane_departure_warning = (uint8_t)LaneDepartureWarning::RIGHT_BOUNDARY_WARNING;
    } else {
        mAlarmEventData.lane_departure_warning = (uint8_t)LaneDepartureWarning::NO_WARNING;
        return true;
    }

    report();

    return true;
}

bool XiaMenShuoQiCanProt::parseFLI2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mFLI2Pkt, canFrame, sizeof(canFrame_t));

    if (mLogLevel >= 2) {
        logd("FLI2: right_tracking=%d, left_tracking=%d, ldws_enable=%d",
             mFLI2Pkt.lane_tracking_status_right_side,
             mFLI2Pkt.lane_tracking_status_left_side,
             mFLI2Pkt.lane_departure_indication_enable_status);
    }

    return true;
}

bool XiaMenShuoQiCanProt::parseWEDRMessage(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mWEDRPkt, canFrame, sizeof(canFrame_t));

    if (mLogLevel >= 2) {
        logd("WEDR: aeb_state=%d, distance=%d, velocity=%d, ldw_lamp=%d, fcw_lamp=%d",
             mWEDRPkt.advanced_emergency_braking_system_state,
             mWEDRPkt.distance_to_main_target_ahead,
             mWEDRPkt.relative_velocity_to_main_target_ahead,
             mWEDRPkt.amber_warning_lamp_status_ldw,
             mWEDRPkt.amber_warning_lamp_status_fcw);
    }

    mAlarmEventData.longitudinal_relative_distance = mWEDRPkt.distance_to_main_target_ahead;
    mAlarmEventData.longitudinal_relative_velocity = (int32_t)mWEDRPkt.relative_velocity_to_main_target_ahead - 125;

    return true;
}

bool XiaMenShuoQiCanProt::parseEBC1Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mEBC1Pkt, canFrame, sizeof(canFrame_t));

    if (mLogLevel >= 2) {
        logd("EBC1: brake_switch=%d, brake_pedal=%d, abs_operational=%d",
             mEBC1Pkt.ebs_brake_switch,
             mEBC1Pkt.ebs_brake_pedal_position,
             mEBC1Pkt.abs_fully_operational);
    }

    mAlarmEventData.brake_pedal_opening = std::min(mEBC1Pkt.ebs_brake_pedal_position * 0.4, 100.0);

    return true;
}

bool XiaMenShuoQiCanProt::parseEBC2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mEBC2Pkt, canFrame, sizeof(canFrame_t));

    if (mEBC2Pkt.mean_front_axle_speed < 0xFB00) {
        mVehicleSpeed = std::floor(mEBC2Pkt.mean_front_axle_speed / 256.0);
    } else {
        //mAlarmEventData.vehicle_speed = 0xFF;
        return true;
    }

    if (mLogLevel >= 2) {
        logd("EBC2: front_axle_speed=%d", mEBC2Pkt.mean_front_axle_speed);
    }

    return true;
}

bool XiaMenShuoQiCanProt::parseVDC2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mVDC2Pkt, canFrame, sizeof(canFrame_t));

    if (mLogLevel >= 2) {
        logd("VDC2: steer_angle=%d, yaw_rate=%d, lat_accel=%d, lon_accel=%d",
             mVDC2Pkt.steer_wheel_angle,
             mVDC2Pkt.yaw_rate,
             mVDC2Pkt.lateral_acceleration,
             mVDC2Pkt.longitudinal_acceleration);
    }

    mAlarmEventData.steering_wheel_angle = std::floor(-31.374 + mVDC2Pkt.steer_wheel_angle * 0.0009766);

    return true;
}

bool XiaMenShuoQiCanProt::parseEEC2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mEEC2Pkt, canFrame, sizeof(canFrame_t));

    if (mLogLevel >= 2) {
        logd("EEC2: accel_pedal_position=%d", mEEC2Pkt.accel_pedal_position1);
    }

    mAlarmEventData.accelerator_pedal_opening = std::min(mEEC2Pkt.accel_pedal_position1 * 0.4, 100.0);

    return true;
}

bool XiaMenShuoQiCanProt::parseETC2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mETC2Pkt, canFrame, sizeof(canFrame_t));

    if (mLogLevel >= 2) {
        logd("ETC2: current_gear=%d", mETC2Pkt.current_gear);
    }

    if (mETC2Pkt.current_gear == 0x7d) {
        mAlarmEventData.gear_status = (uint8_t)GearStatus::NEUTRAL;
    } else if (mETC2Pkt.current_gear < 0x7d) {
        mAlarmEventData.gear_status = (uint8_t)GearStatus::REVERSE;
    } else if (mETC2Pkt.current_gear > 0x7d) {
        mAlarmEventData.gear_status = (uint8_t)GearStatus::DRIVE;
    }

    return true;
}

bool XiaMenShuoQiCanProt::parseOEL_27Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mOEL_27Pkt, canFrame, sizeof(canFrame_t));

    if (mLogLevel >= 2) {
        logd("OEL_27: turn_signal=%d, fcw_lamp=%d",
             mOEL_27Pkt.turn_signal_switch,
             mOEL_27Pkt.amber_warning_lamp_status_fcw);
    }

    if (mOEL_27Pkt.amber_warning_lamp_status_fcw == (uint8_t)AmberWarningLampStatus::LAMP_ON) {
        mAlarmEventData.device_status_fault_code = 0b11;  // 雷达和前视摄像头异常
    }

    return true;
}

bool XiaMenShuoQiCanProt::parseUDASMessage(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mUDASPkt, canFrame, sizeof(canFrame_t));

    if (mLogLevel >= 2) {
        logd("UDAS: RA=%d, RB=%d, RC=%d, RD=%d, FA=%d, FB=%d, FC=%d, FD=%d",
             mUDASPkt.ra,
             mUDASPkt.rb,
             mUDASPkt.rc,
             mUDASPkt.rd,
             mUDASPkt.fa,
             mUDASPkt.fb,
             mUDASPkt.fc,
             mUDASPkt.fd);
    }

    return true;
}

void XiaMenShuoQiCanProt::run() {
    while (!exiting()) {
        LBS lbs;
        DeviceHub::getInstance().getGpsLbs(lbs);

        {
            std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
            mAlarmEventData.vehicle_speed = DeviceHub::getInstance().getCarSpeed();
            updateGpsData(lbs);
            addRealTimeData(lbs);
            outputAlgo();
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(77));
    }
}

void XiaMenShuoQiCanProt::updateGpsData(LBS lbs){
    // 以度为单位的纬度值乘以 10 的 6 次方，精确到百万分之一度
    mAlarmEventData.latitude = lbs.lat_x1kw / 10;
    mAlarmEventData.longitude = lbs.lng_x1kw / 10;
    mAlarmEventData.altitude = lbs.alt_x10 / 10;
}

void XiaMenShuoQiCanProt::outputAlgo() {
    AlgoOutput_S msg;

    std::lock_guard<std::mutex> lg(mAlgoRcdMapMtx);
    for (auto &[p, ts] : mLastAlgoEvtMap) {
        if (ts.elapsed() < 3000) {
            switch(p.first){
                case EVT_TYPE_BSD_Left: {
                    msg.leftBsd = true;
                    mAlarmEventData.alarm_event_type = 0x31;
                    mAlarmEventData.alarm_level = p.second == 1 ? 2 : 1;
                    report();
                    break;
                }
                case EVT_TYPE_BSD_Right: {
                    msg.rightBsd = true;
                    mAlarmEventData.alarm_event_type = 0x32;
                    mAlarmEventData.alarm_level = p.second == 1 ? 2 : 1;
                    report();
                    break;
                }
                case EVT_TYPE_DMS_ABSENCE: {
                    msg.absense = true;
                    mAlarmEventData.alarm_event_type = 0x0F;
                    mAlarmEventData.alarm_level = 2;
                    report();
                    break;
                }
                case EVT_TYPE_DMS_FATIGUE_Eye: {
                    msg.eyeclose = true;
                    mAlarmEventData.alarm_event_type = 0x10;
                    mAlarmEventData.alarm_level = 2;
                    report();
                    break;
                }
                case EVT_TYPE_HOD_HANDSOFF: {
                    msg.handsoff = true;
                    mAlarmEventData.alarm_event_type = 0x11;
                    mAlarmEventData.alarm_level = 2;
                    report();
                    break;
                }
            }
        }
    }

    msgEnque((void *)&msg, sizeof(msg));
}

void XiaMenShuoQiCanProt::addRealTimeData(LBS lbs) {
    uint64_t timestamp = my::getTimestampMs();

    // AEB功能状态(1打开,0关闭)
    uint8_t aeb_status = (mAlarmEventData.alarm_event_type == (uint8_t)AlarmEventType::AEB_WARNING) ? 1 : 0;

    // 实时报警状态(0 正常行驶, 1 FCW一级预警, 2 FCW二级预警, 3 AEB制动)
    uint8_t alarm_status = 0;
    if (mAlarmEventData.forward_collision_warning_level == (uint8_t)ForwardCollisionWarningLevel::LEVEL_1_WARNING) {
        alarm_status = 1;
    } else if (mAlarmEventData.forward_collision_warning_level == (uint8_t)ForwardCollisionWarningLevel::LEVEL_2_WARNING) {
        alarm_status = 2;
    } else if (mAlarmEventData.aeb_brake_status != (uint8_t)AEBBrakingStatus::NO_BRAKING &&
               mAlarmEventData.aeb_brake_status != (uint8_t)AEBBrakingStatus::INVALID) {
        alarm_status = 3;
    }

    // GPS定位状态(0未定位,1定位)
    uint8_t gps_status = lbs.status;

    // 南北纬标志(0 北纬, 1 南纬)
    uint8_t ns_flag = (lbs.lat_x1kw < 0) ? 1 : 0;

    // 东西经标志(0东经, 1 西经)
    uint8_t ew_flag = (lbs.lng_x1kw < 0) ? 1 : 0;

    // 纬度(度)
    double latitude = lbs.lat_x1kw / 10000000.0;

    // 经度(度)
    double longitude = lbs.lng_x1kw / 10000000.0;

    // 高度(米)
    double altitude = lbs.alt_x10 / 10.0;

    // 速度(km/h)
    double speed = DeviceHub::getInstance().getCarSpeed();

    // 方向(0-359度)
    double direction = lbs.dir_x100 / 100.0;

    // 纵向车速(Km/h 前进为正)
    double longitudinal_speed = speed;

    // 横向车速(km/h, 向左为正)
    double lateral_speed = 0.0;

    // 纵向加速度(m/s2)
    double longitudinal_accel = 0.0;
    if (mVDC2Pkt.longitudinal_acceleration != 0xFF) {
        longitudinal_accel = mVDC2Pkt.longitudinal_acceleration * 0.1 - 12.5;
    }

    // 横向加速度(m/s2)
    double lateral_accel = 0.0;
    if (mVDC2Pkt.lateral_acceleration != 0xFFFF) {
        lateral_accel = mVDC2Pkt.lateral_acceleration * 0.0004883 - 15.687;
    }

    // 横摆角速度(rad/s)
    double yaw_rate = 0.0;
    if (mVDC2Pkt.yaw_rate != 0xFFFF) {
        yaw_rate = mVDC2Pkt.yaw_rate * 0.0001221 - 3.92;
    }

    // 档位(N 0, R 1, D 2)
    uint8_t gear = 0;
    if (mAlarmEventData.gear_status == (uint8_t)GearStatus::NEUTRAL) {
        gear = 0;
    } else if (mAlarmEventData.gear_status == (uint8_t)GearStatus::REVERSE) {
        gear = 1;
    } else if (mAlarmEventData.gear_status == (uint8_t)GearStatus::DRIVE) {
        gear = 2;
    }

    // 油门踏板开度(0~100%)
    double throttle_opening =
        (mAlarmEventData.accelerator_pedal_opening == 0xFF) ? 0.0 : mAlarmEventData.accelerator_pedal_opening;

    // 刹车踏板开度(0~100%)
    double brake_opening = (mAlarmEventData.brake_pedal_opening == 0xFF) ? 0.0 : mAlarmEventData.brake_pedal_opening;

    // 转向灯信号(0未开启, 1左转, 2右转)
    uint8_t turn_signal = 0;
    if (mOEL_27Pkt.turn_signal_switch == (uint8_t)TurnSignalSwitchStatus::LEFT_TURN_FLASHING) {
        turn_signal = 1;
    } else if (mOEL_27Pkt.turn_signal_switch == (uint8_t)TurnSignalSwitchStatus::RIGHT_TURN_FLASHING) {
        turn_signal = 2;
    }

    // 目标纵向相对距离(m)
    double target_longitudinal_distance = mAlarmEventData.longitudinal_relative_distance * 0.1;

    // 目标横向相对距离(m)
    double target_lateral_distance = mAlarmEventData.lateral_relative_distance * 0.1;

    // TTC碰撞时间(s)
    double ttc = (mAlarmEventData.ttc_collision_time == 0) ? 999.000000 : mAlarmEventData.ttc_collision_time * 0.1;
    ttc = (mIsTriggerAlarm ? 2.34 : ttc);

    char csv_line[512];
    snprintf(csv_line,
             sizeof(csv_line),
             "%lld, %d, %d, %d, %d, %d, %.6f, %.6f, %.6f, %.6f, %.6f, %.6f, %.6f, %.6f, %.6f, %.6f, %d, %.0f, %.0f, "
             "%d, %.6f, %.6f, %.6f\n",
             timestamp,                     // 时间戳
             aeb_status,                    // AEB功能状态
             alarm_status,                  // 实时报警状态
             gps_status,                    // GPS定位状态
             ns_flag,                       // 南北纬标志
             ew_flag,                       // 东西经标志
             latitude,                      // 纬度(度)
             longitude,                     // 经度(度)
             altitude,                      // 高度(米)
             speed,                         // 速度(km/h)
             direction,                     // 方向(0-359度)
             longitudinal_speed,            // 纵向车速(Km/h)
             lateral_speed,                 // 横向车速(km/h)
             longitudinal_accel,            // 纵向加速度
             lateral_accel,                 // 横向加速度
             yaw_rate,                      // 横摆角速度(rad/s)
             gear,                          // 档位
             throttle_opening,              // 油门踏板开度(0~100%)
             brake_opening,                 // 刹车踏板开度(0~100%)
             turn_signal,                   // 转向灯信号
             target_longitudinal_distance,  // 目标纵向相对距离(m)
             target_lateral_distance,       // 目标横向相对距离(m)
             ttc                            // TTC碰撞时间(s)
    );

    mAttDataList.push_back(std::string(csv_line));

    const size_t MAX_FRAMES = 130;
    while (mAttDataList.size() > MAX_FRAMES) {
        mAttDataList.pop_front();
    }

    do {
        std::lock_guard<std::mutex> lg(mAttGenMtx);
        auto it = mAttGenMp.begin();
        while (it != mAttGenMp.end()) {
            if (it->first < my::timestamp::now().utc_milliseconds()) {
                generateAttachmentFile(it->second.c_str());
                mAttGenMp.erase(it++);
                mIsTriggerAlarm = false;
            } else {
                break;
            }
        }

    } while (0);
}

void XiaMenShuoQiCanProt::report() {
    // std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
    auto obj = std::make_pair(mAlarmEventData.alarm_event_type, mAlarmEventData.alarm_level);
    if (!mIsTriggerAlarm && mLastAlarmTsMap.find(obj) != mLastAlarmTsMap.end() &&
        mLastAlarmTsMap[obj].elapsed() < 3000) {
        return;
    }
    mLastAlarmTsMap[obj] = my::timestamp::now();

    mAlarmEventData.alarm_id = mAlarmid++;
    mAlarmEventData.ts = my::timestamp::seconds_from_19700101();

    expand::XiaMenShuoQiMsg message;
    message.mUtcTime = my::timestamp::utc_milliseconds();

    message.alarm_id = mAlarmEventData.alarm_id;
    message.flag_status = mAlarmEventData.flag_status;
    message.alarm_event_type = mAlarmEventData.alarm_event_type;
    message.alarm_level = mAlarmEventData.alarm_level;
    message.target_speed = mAlarmEventData.target_speed;
    message.target_obstacle_type = mAlarmEventData.target_obstacle_type;
    message.ttc_collision_time = mAlarmEventData.ttc_collision_time;
    message.longitudinal_relative_distance = mAlarmEventData.longitudinal_relative_distance;
    message.lateral_relative_distance = mAlarmEventData.lateral_relative_distance;
    message.ultrasonic_distance = mAlarmEventData.ultrasonic_distance;
    message.longitudinal_relative_velocity = mAlarmEventData.longitudinal_relative_velocity;
    message.forward_collision_warning_level = mAlarmEventData.forward_collision_warning_level;
    message.lane_departure_warning = mAlarmEventData.lane_departure_warning;
    message.left_lane_line_type = mAlarmEventData.left_lane_line_type;
    message.right_lane_line_type = mAlarmEventData.right_lane_line_type;
    message.device_status_fault_code = mAlarmEventData.device_status_fault_code;
    message.aeb_brake_switch_status = mAlarmEventData.aeb_brake_switch_status;
    message.aeb_brake_status = mAlarmEventData.aeb_brake_status;
    message.steering_wheel_angle = mAlarmEventData.steering_wheel_angle;
    message.steering_wheel_status = mAlarmEventData.steering_wheel_status;
    message.gear_status = mAlarmEventData.gear_status;
    message.accelerator_pedal_opening = mAlarmEventData.accelerator_pedal_opening;
    message.brake_pedal_opening = mAlarmEventData.brake_pedal_opening;
    message.vehicle_speed = mAlarmEventData.vehicle_speed;
    message.altitude = mAlarmEventData.altitude;
    message.latitude = mAlarmEventData.latitude;
    message.longitude = mAlarmEventData.longitude;
    message.ts = mAlarmEventData.ts;
    message.vehicle_status = mAlarmEventData.vehicle_status;

    msgpack::sbuffer sbuf;
    msgpack::pack(sbuf, message);

    ExpandSet::getInstance().sendLibFlow(XIAMEN_SHUOQI_LIBFLOW_TOPIC, sbuf.data(), sbuf.size());
    logd("report xiamenshuoqi alarm %d, size %d, ts %u", message.alarm_id, sbuf.size(), message.ts);
}

bool XiaMenShuoQiCanProt::triggerFakeEvent(const char* eventType, const char* level, std::string& ack) {
    if (!eventType || !level) {
        ack = "Invalid parameters";
        return false;
    }

    int levelInt = atoi(level);
    if (levelInt < 1 || levelInt > 2) {
        ack = "Invalid level. Must be 1, 2";
        return false;
    }
    do {
        std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

        mAlarmEventData.alarm_level = (uint8_t)levelInt;
        mAlarmEventData.target_obstacle_type = (uint8_t)TargetObstacleType::VEHICLE;
        mAlarmEventData.ttc_collision_time = 20;               // 2.0 seconds
        mAlarmEventData.longitudinal_relative_distance = 300;  // 30.0 meters
        mAlarmEventData.vehicle_speed = 60;                    // 60 km/h

        std::string eventStr(eventType);

        if (eventStr == "aeb_warning") {
            mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::AEB_WARNING;
            mAlarmEventData.forward_collision_warning_level =
                (levelInt == 1) ? (uint8_t)ForwardCollisionWarningLevel::LEVEL_1_WARNING
                                : (uint8_t)ForwardCollisionWarningLevel::LEVEL_2_WARNING;
            ack = "Triggered AEB warning event with level " + std::string(level);
        } else if (eventStr == "aeb_braking") {
            mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::AEB_BRAKING;
            mAlarmEventData.aeb_brake_status = (uint8_t)AEBBrakingStatus::BINOCULAR_BRAKING;
            ack = "Triggered AEB braking event with level " + std::string(level);
        } else if (eventStr == "ldw_left") {
            mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::LANE_DEPARTURE_WARNING;
            mAlarmEventData.lane_departure_warning = (uint8_t)LaneDepartureWarning::LEFT_BOUNDARY_WARNING;
            mAlarmEventData.left_lane_line_type = (uint8_t)LaneLineType::SOLID_LINE;
            ack = "Triggered left lane departure warning with level " + std::string(level);
        } else if (eventStr == "ldw_right") {
            mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::LANE_DEPARTURE_WARNING;
            mAlarmEventData.lane_departure_warning = (uint8_t)LaneDepartureWarning::RIGHT_BOUNDARY_WARNING;
            mAlarmEventData.right_lane_line_type = (uint8_t)LaneLineType::SOLID_LINE;
            ack = "Triggered right lane departure warning with level " + std::string(level);
        } else if (eventStr == "eyeclose" || eventStr == "absence" || eventStr == "handsoff" || eventStr == "fbsd" ||
                   eventStr == "bsd") {
            const static std::map<std::string,
                                  std::tuple<EVT_TYPE, std::string /*algoName*/, std::string /*eventName*/>>
                mp = {
                    {"eyeclose", std::make_tuple(EVT_TYPE_DMS_FATIGUE_Eye, "dms", "eyeclose")},
                    {"absence", std::make_tuple(EVT_TYPE_DMS_ABSENCE, "dms", "absence")},
                    {"handsoff", std::make_tuple(EVT_TYPE_HOD_HANDSOFF, "hod", "handsoff")},
                    {"fbsd", std::make_tuple(EVT_TYPE_BSD_Left, "fbsd", "LFBsdWarningL1")},
                    {"bsd", std::make_tuple(EVT_TYPE_BSD_Right, "bsd", "bsd")},
                };
            auto &am = AlgoManager::getInstance();
            std::shared_ptr<Event> sp = make_shared<Event>("dms", EVT_TYPE_DMS_FATIGUE_Eye);
            sp->c.event = eventStr;
            sp->c.frameId = 1;
            sp->c.ts = time(0);
            sp->c.speed = 55.5;
            sp->c.level = 1;
            am.push(sp);
        } else {
            ack =
                "Unknown event type: " + eventStr + "\nSupported types: aeb_warning, aeb_braking, ldw_left, ldw_right";
            return false;
        }
    } while (0);
    mIsTriggerAlarm = true;
    report();

    logd("Fake event triggered: %s, level: %s", eventType, level);
    return true;
}

bool XiaMenShuoQiCanProt::generateAttachmentFile(const char* path) {
    // std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    // logd("generate attachment file, path: %s", path);
    my::string tmpPath(path);
    tmpPath += ".tmp";
    std::ofstream file(tmpPath.c_str(), std::ios::out);
    if (!file.is_open()) {
        logd("failed to open file: %s", tmpPath.c_str());
        return false;
    }

    size_t totalBytes = 0;
    for (const auto& it : mAttDataList) {
        file.write(it.c_str(), it.length());
        if (file.fail()) {
            logd("failed to write data to file, size %zu", it.length());
            file.close();
            return false;
        }
        totalBytes += it.length();
    }
    file.close();

    return my::file::rename(tmpPath.c_str(), path);
}

bool XiaMenShuoQiCanProt::prepareAttachmentFile(const char *path, std::string &ack) {
    if (mAttDataList.empty()) {
        ack = "No real-time data available to generate attachment file";
        return false;
    }

    if (!path || strlen(path) == 0) {
        ack = "Invalid path parameter";
        return false;
    }

    std::lock_guard<std::mutex> lock(mAttGenMtx);
    mAttGenMp.insert(std::make_pair(my::timestamp::now().utc_milliseconds() + 5000, std::string(path)));

    ack = "OK";
    return true;
}

}  // namespace minieye